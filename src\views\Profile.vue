<template>
  <div class="profile min-h-screen bg-gray-50">
    <header class="header sticky top-0 flex items-center px-6 py-4 bg-white shadow-sm z-10">
      <el-button
        type="text"
        :icon="ChevronLeft"
        class="text-gray-600 p-0 min-w-0 hover:text-blue-600 transition-colors"
        @click="router.push('/dashboard')"
      />
      <h1 class="text-xl font-semibold text-gray-800 ml-3">我的</h1>
    </header>

    <!-- 骨架屏加载 -->
    <div v-if="loading" class="p-6 space-y-6">
      <el-skeleton :rows="3" animated class="w-full" />
      <el-skeleton :rows="1" animated class="w-full" />
      <el-skeleton :rows="2" animated class="w-full" />
    </div>

    <!-- 主内容 -->
    <div v-else class="main p-6 space-y-6">
      <el-card shadow="always" class="user-card border-0 rounded-lg shadow-md bg-white">
        <div class="user-info flex items-center gap-4 p-4">
          <el-avatar :size="64" :src="user.headurl || defaultAvatar" class="rounded-full">
            {{ user.truename ? user.truename[0] : '用户' }}
          </el-avatar>
          <div class="user-details">
            <h2 class="user-name text-xl font-semibold text-gray-800">
              {{ user.truename || '未设置姓名' }}
            </h2>
            <p class="user-role text-sm text-gray-500 mt-1">{{ userRole }}</p>
          </div>
        </div>
      </el-card>

      <el-card shadow="always" class="logout-card border-0 rounded-lg shadow-md bg-white">
        <div
          class="logout flex items-center gap-3 p-4 text-red-600 hover:bg-gray-100 transition-colors cursor-pointer"
          @click="handleLogout"
        >
          <el-icon class="text-xl"><LogOut /></el-icon>
          <span class="text-base font-medium">退出登录</span>
        </div>
      </el-card>

      <div class="footer text-center text-sm text-gray-500">
        <p>未纳入社会化照料服务的特困人员监管模块</p>
        <p class="mt-1">照料人监管系统 v1.0.0</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ChevronLeft, LogOut } from 'lucide-vue-next'
import { ElMessage } from 'element-plus'
import { getUserProfile, logout } from '@/api/request' // 新增 API 调用

const router = useRouter()
const loading = ref(true) // 骨架屏加载状态
const user = ref({})
const defaultAvatar = '/placeholder.svg?height=64&width=64'

// 计算用户角色描述
const userRole = computed(() => {
  if (!user.value.town_id || !user.value.lbigv) return '未设置角色信息'
  return `${user.value.ltown || '未知乡镇'} ${user.value.lbigv || ''} 村民政协理员`
})

const fetchUserProfile = async () => {
  try {
    loading.value = true
    const response = await getUserProfile()
    if (response.code === 200) {
      user.value = response.data.user
    } else {
      ElMessage.error('获取用户信息失败: ' + response.message)
    }
  } catch (error) {
    ElMessage.error('获取用户信息失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleLogout = async () => {
  try {
    const response = await logout()
    if (response.code === 200) {
      ElMessage.success('退出登录成功')
      router.push('/login')
    } else {
      ElMessage.error('退出登录失败: ' + response.message)
    }
  } catch (error) {
    ElMessage.error('退出登录失败: ' + error.message)
  }
}

onMounted(() => {
  fetchUserProfile()
})
</script>
