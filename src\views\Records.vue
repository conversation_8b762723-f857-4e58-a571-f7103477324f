<template>
  <div class="records min-h-screen bg-gray-50">
    <header class="header sticky top-0 flex items-center px-6 py-4 bg-white shadow-sm z-10">
      <el-button
        type="text"
        :icon="ChevronLeft"
        class="text-gray-600 p-0 min-w-0 hover:text-blue-600 transition-colors"
        @click="router.push('/dashboard')"
      />
      <h1 class="text-xl font-semibold text-gray-800 ml-3">监管记录</h1>
    </header>

    <!-- 骨架屏加载 -->
    <div v-if="loading" class="p-6 space-y-4">
      <el-skeleton :rows="1" animated class="w-full" />
      <el-skeleton :rows="1" animated class="w-full" />
      <el-skeleton :rows="5" animated class="w-full" />
    </div>

    <!-- 主内容 -->
    <div v-else>
      <div class="search-bar sticky top-16 px-6 py-4 bg-white shadow-sm z-10">
        <div class="flex flex-col gap-4">
          <el-select
            v-model="selectedTown"
            placeholder="选择乡镇"
            class="w-full rounded-md border-gray-300"
            disabled
          >
            <el-option v-for="town in towns" :key="town.id" :label="town.name" :value="town.id" />
          </el-select>
          <el-select
            v-model="selectedVillage"
            placeholder="选择村"
            class="w-full rounded-md border-gray-300 focus:ring-blue-500 focus:border-blue-500"
            @change="handleVillageChange"
          >
            <el-option
              v-for="big_village in villages"
              :key="big_village"
              :label="big_village"
              :value="big_village"
            />
          </el-select>
          <el-input
            v-model="searchQuery"
            placeholder="搜索特困人员姓名"
            clearable
            prefix-icon="Search"
            class="w-full rounded-md border-gray-300 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      <main class="main p-6">
        <div class="list space-y-4">
          <el-card
            v-for="record in filteredRecords"
            :key="record.id"
            shadow="hover"
            class="record-card border-0 rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer bg-white"
            @click="handleViewRecord(record.id)"
          >
            <div class="flex justify-between items-center p-4">
              <div class="flex-1">
                <h3 class="text-lg font-semibold text-gray-800">
                  {{ record.name }} ({{ record.age }}岁)
                </h3>
                <p class="text-sm text-gray-500 mt-1">
                  采集时间: {{ formatDate(record.collect_time) }}
                </p>
                <p v-if="record.last_photo_time" class="text-sm text-gray-500 mt-1">
                  本月最后拍照: {{ formatDate(record.last_photo_time) }}
                </p>
                <p v-else class="text-sm text-gray-500 mt-1">本月最后拍照: 无照片</p>
                <div class="flex gap-3 mt-2">
                  <span v-if="record.has_photos" class="flex items-center text-sm text-gray-500">
                    <el-icon><Image /></el-icon> 照片
                  </span>
                  <span v-if="record.has_video" class="flex items-center text-sm text-gray-500">
                    <el-icon><Video /></el-icon> 视频
                  </span>
                </div>
                <!-- 展示 sh_state 和 sh_text -->
                <p
                  v-if="record.sh_state"
                  class="text-sm mt-1"
                  :class="getStateClass(record.sh_state)"
                >
                  审核状态: {{ record.sh_state }}
                </p>
                <p v-if="record.sh_text" class="text-sm text-gray-500 mt-1">
                  审核备注: {{ record.sh_text }}
                </p>
              </div>
              <div class="flex items-center gap-3">
                <el-tag
                  v-if="record.is_latest_this_month"
                  type="primary"
                  size="small"
                  class="rounded-full px-2 py-1 bg-blue-600 text-white"
                >
                  本月新采集
                </el-tag>
                <el-icon class="text-gray-500"><ChevronRight /></el-icon>
              </div>
            </div>
          </el-card>

          <div v-if="filteredRecords.length === 0" class="empty py-8 text-center">
            <el-empty description="未找到匹配的监管记录" :image-size="100" />
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getRecordList, updateVillage } from '@/api/request'
import { ElMessage } from 'element-plus'
import { ChevronLeft, ChevronRight, Image, Search, Video } from 'lucide-vue-next'

const router = useRouter()
const searchQuery = ref('')
const records = ref([])
const selectedTown = ref(null)
const selectedVillage = ref(null)
const towns = ref([])
const villages = ref([])
const loading = ref(true)

const filteredRecords = computed(() => {
  if (!records.value) return []
  return records.value.filter((record) => record.name && record.name.includes(searchQuery.value))
})

const formatDate = (timestamp) => {
  if (!timestamp) return '未知时间'
  const date = new Date(timestamp * 1000)
  return `${date.toLocaleDateString('zh-CN')} ${date.toLocaleTimeString('zh-CN', { hour12: false })}`
}

// 根据 sh_state 返回颜色类
const getStateClass = (state) => {
  if (state === '不符合') return 'text-red-600'
  if (state === '符合') return 'text-green-600'
  return 'text-gray-500'
}

const fetchRecords = async () => {
  try {
    loading.value = true
    const response = await getRecordList({ big_village: selectedVillage.value })
    if (response.code === 200) {
      records.value = response.data.records || []
      towns.value = response.data.towns || []
      villages.value = response.data.villages || []
      selectedTown.value = towns.value[0]?.id || null
      if (!selectedVillage.value && response.data.selected_village) {
        selectedVillage.value = response.data.selected_village
      }
    } else {
      ElMessage.error('获取监管记录失败: ' + response.message)
    }
  } catch (error) {
    ElMessage.error('获取监管记录失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleVillageChange = async (village) => {
  try {
    loading.value = true
    const response = await updateVillage({ big_village: village })
    if (response.code === 200) {
      selectedVillage.value = village
      await fetchRecords()
      ElMessage.success('行政村已更新')
    } else {
      ElMessage.error('更新行政村失败: ' + response.message)
    }
  } catch (error) {
    ElMessage.error('更新行政村失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleViewRecord = (id) => {
  router.push(`/records/${id}`)
}

onMounted(() => {
  fetchRecords()
})
</script>
