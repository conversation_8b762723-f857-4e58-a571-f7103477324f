import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
// import vueDevTools from 'vite-plugin-vue-devtools'
import tailwindcss from '@tailwindcss/vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    tailwindcss(),
    vue(),
    // vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    historyApiFallback: true,
    proxy: {
      "/api": {
        // target: "https://ext.dzfs.75prc.cn/",
        target: "http://wuchengweinaru.localhost/",
        changeOrigin: true,
        rewrite: (path) => path,
      },
      "/storage": {
        // target: "https://ext.dzfs.75prc.cn/",
        target: "http://wuchengweinaru.localhost/",
        changeOrigin: true,
        rewrite: (path) => path,
      },
      "/upload": {
        // target: "https://ext.dzfs.75prc.cn/",
        target: "http://wuchengweinaru.localhost/",
        changeOrigin: true,
        rewrite: (path) => path,
      },
    },
  },
})
