<template>
  <div class="record-detail min-h-screen bg-gray-50">
    <header class="sticky top-0 flex items-center px-6 py-4 bg-white shadow-sm z-20">
      <el-button
        type="text"
        :icon="ChevronLeft"
        class="text-gray-600 p-0 min-w-0 hover:text-blue-600 transition-colors"
        @click="router.push('/records')"
      />
      <h1 class="text-xl font-semibold text-gray-800 ml-3">记录详情</h1>
    </header>

    <!-- 骨架屏加载 -->
    <div v-if="loading" class="p-6 space-y-6">
      <el-skeleton :rows="3" animated class="w-full" />
      <el-skeleton :rows="4" animated class="w-full" />
      <el-skeleton :rows="6" animated class="w-full" />
    </div>

    <!-- 主内容 -->
    <div v-else class="px-6 pt-6 pb-24 space-y-6">
      <el-card shadow="always" class="border-0 rounded-lg shadow-md bg-white">
        <h2 class="text-xl font-semibold text-gray-800 mb-3">
          {{ record.name }} ({{ record.age || '未知' }}岁)
        </h2>
        <p class="text-sm text-gray-500">照料人: {{ record.relation_name || '无' }}</p>
        <p class="text-sm text-gray-500">{{ formatDate(record.collect_time) }}</p>
        <!-- 展示 sh_state 和 sh_text -->
        <p v-if="order?.sh_state" class="text-sm mt-1" :class="getStateClass(order.sh_state)">
          审核状态: {{ order.sh_state }}
        </p>
        <p v-if="order?.sh_text" class="text-sm text-gray-500 mt-1">
          审核备注: {{ order.sh_text }}
        </p>
      </el-card>

      <el-card shadow="always" class="border-0 rounded-lg shadow-md bg-white">
        <h3 class="text-lg font-semibold text-gray-700 mb-3">本月订单备注</h3>
        <el-input
          v-model="context"
          type="textarea"
          :rows="4"
          placeholder="请输入备注"
          @input="saveContext"
          class="w-full rounded-md border-gray-300 focus:ring-blue-500 focus:border-blue-500"
          :disabled="uploading"
        />
      </el-card>

      <div class="bg-white rounded-lg shadow-md">
        <el-tabs v-model="activeTab" class="px-4 py-2">
          <el-tab-pane v-for="item in items" :key="item.id" :label="item.name" :name="item.name">
            <el-card shadow="always" class="m-4 border-0 rounded-lg shadow-md bg-white">
              <h3 class="text-lg font-semibold text-gray-700 mb-4">{{ item.name }}</h3>
              <el-row :gutter="20">
                <el-col
                  v-for="(file, index) in getFiles(item.id)"
                  :key="file.id"
                  :span="12"
                  class="py-2"
                >
                  <div
                    class="w-full h-full relative bg-gray-100 rounded-lg overflow-hidden pt-[100%]"
                  >
                    <!-- 图片或视频预览区 -->
                    <template v-if="file.f_type === 'video'">
                      <video
                        :src="`/storage/${file.path}`"
                        controls
                        class="absolute top-0 left-0 w-full h-full object-cover"
                      ></video>
                    </template>
                    <template v-else>
                      <img
                        :src="`/storage/${file.path}`"
                        :alt="`${item.name} ${index + 1}`"
                        class="absolute top-0 left-0 w-full h-full object-cover"
                      />
                    </template>

                    <!-- 操作按钮区 -->
                    <div class="absolute top-2 right-2 flex space-x-2">
                      <el-button
                        type="danger"
                        size="small"
                        circle
                        class="bg-red-600 hover:bg-red-700 text-white"
                        @click="deleteFile(file.id)"
                        :disabled="uploading"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                      <el-button
                        type="primary"
                        size="small"
                        circle
                        class="bg-blue-600 hover:bg-blue-700 text-white"
                        @click="openPreview(file)"
                        :disabled="uploading"
                      >
                        <el-icon><ZoomIn /></el-icon>
                      </el-button>
                    </div>

                    <!-- 底部信息区 -->
                    <div
                      class="absolute bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm text-gray-700 text-xs px-2 py-1"
                    >
                      <div>{{ file.t }}</div>
                      <div>
                        {{ formatTimestamp(file.addtime, 'YYYY-MM-DD HH:mm:ss') }}
                      </div>
                    </div>
                  </div>
                </el-col>

                <el-col :span="12" class="py-2">
                  <div
                    class="w-full pt-[100%] relative bg-gray-200 rounded-lg overflow-hidden flex items-center justify-center cursor-pointer hover:bg-blue-500 transition-colors"
                    :class="{ 'pointer-events-none opacity-50': uploading }"
                    @click="openMediaTypeDialog(item.id)"
                  >
                    <el-icon class="text-gray-500 text-3xl"><Plus /></el-icon>
                  </div>
                  <input
                    :type="'file'"
                    :key="`fileInput-${item.id}`"
                    :accept="item.name === '满意度视频' ? 'video/*' : 'image/*'"
                    capture="camera"
                    :ref="`fileInput-${item.id}`"
                    class="hidden"
                    @change="handleFileUpload($event, item.id, item.name)"
                  />
                </el-col>
              </el-row>
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 完成监督按钮 -->
      <el-button
        type="primary"
        class="w-full mt-4"
        @click="handleCompleteSupervision"
        :disabled="!isUploadComplete || uploading"
      >
        完成监督
      </el-button>
    </div>

    <!-- 选择上传类型对话框 -->
    <el-dialog
      title="选择上传类型"
      v-model="mediaTypeDialogVisible"
      width="300px"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <div class="space-y-4">
        <el-select
          v-model="selectedCategory"
          placeholder="请选择类别"
          class="w-full"
          @change="categorySelected"
          :disabled="uploading"
        >
          <el-option
            v-for="option in currentCategoryOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
            :disabled="isCategoryUploaded(option.value)"
          />
        </el-select>
        <div v-if="!currentCategoryOptions.length" class="text-center text-gray-500">
          暂无可用类别
        </div>
        <div class="flex justify-around">
          <el-button
            type="primary"
            :disabled="!selectedCategory || uploading"
            @click="selectMediaType"
            class="flex-1 mx-2"
          >
            {{ currentItemName === '满意度视频' ? '录视频' : '拍照' }}
          </el-button>
        </div>
      </div>
      <template #footer>
        <el-button @click="cancelMediaTypeDialog" :disabled="uploading">取消</el-button>
      </template>
    </el-dialog>

    <!-- 上传进度对话框 -->
    <el-dialog
      title="正在上传"
      v-model="uploading"
      width="350px"
      :close-on-click-modal="false"
      :show-close="false"
      :modal="true"
    >
      <div class="flex flex-col items-center justify-center p-4">
        <el-progress
          :percentage="progress"
          :format="() => `${progress}%`"
          :stroke-width="10"
          :color="'#409EFF'"
          class="w-full"
        />
        <div class="mt-3 text-center space-y-1">
          <p class="text-gray-600 text-sm">
            {{ progress < 100 ? '上传中，请稍候...' : '上传完成' }}
          </p>
          <p v-if="uploadSpeed > 0" class="text-gray-500 text-xs">
            上传速度: {{ formatSpeed(uploadSpeed) }}
          </p>
          <p v-if="totalSize > 0" class="text-gray-500 text-xs">
            {{ formatSize(uploadedSize) }} / {{ formatSize(totalSize) }}
          </p>
        </div>
      </div>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog
      title="文件预览"
      v-model="previewDialogVisible"
      width="80%"
      :close-on-click-modal="true"
    >
      <div class="w-full h-[60vh] flex items-center justify-center bg-gray-100">
        <template v-if="previewFile?.f_type === 'video'">
          <video
            :src="previewFile ? `/storage/${previewFile.path}` : ''"
            controls
            class="max-w-full max-h-full object-contain"
          ></video>
        </template>
        <template v-else>
          <img
            :src="previewFile ? `/storage/${previewFile.path}` : ''"
            :alt="previewFile ? previewFile.path : ''"
            class="max-w-full max-h-full object-contain"
          />
        </template>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  getRecordDetail,
  saveOrderContext,
  uploadOrderPhoto,
  deleteOrderFile,
  completeSupervision,
} from '@/api/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ChevronLeft, Plus, Delete, ZoomIn } from 'lucide-vue-next'
import { formatTimestamp } from '@/utils/format'
import imageCompression from 'browser-image-compression'

const router = useRouter()
const route = useRoute()
const recordId = route.params.id

const record = ref({})
const items = ref([])
const files = ref([])
const activeTab = ref('')
const context = ref('')
const fileInput = ref({})
const order = ref(null)
const loading = ref(true)
const mediaTypeDialogVisible = ref(false)
const currentItemId = ref(null)
const currentItemName = ref(null)
const selectedCategory = ref(null)
const previewDialogVisible = ref(false)
const previewFile = ref(null)
const uploading = ref(false)
const progress = ref(0)
const uploadSpeed = ref(0)
const uploadedSize = ref(0)
const totalSize = ref(0)
const lastTime = ref(0)
const lastLoaded = ref(0)

const currentCategoryOptions = computed(() => {
  const item = items.value.find((i) => i.id === currentItemId.value)
  return item?.need_file_desc || []
})

const formatDate = (timestamp) => {
  const date = new Date(timestamp * 1000)
  return `${date.toLocaleDateString('zh-CN')} ${date.toLocaleTimeString('zh-CN', { hour12: false })}`
}

const formatSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatSpeed = (bytesPerSecond) => {
  return formatSize(bytesPerSecond) + '/s'
}

const isCategoryUploaded = (category) => {
  return files.value.some((file) => file.item === currentItemId.value && file.t === category)
}

const isUploadComplete = computed(() => {
  return items.value.every((item) => {
    // 如果 need_file_num 为 0，则没有限制，跳过检查
    if (item.need_file_num === 0) {
      return true
    }

    // 计算该项目已上传的文件数量
    const uploadedCount = files.value.filter((file) => file.item === item.id).length

    // 检查是否满足最小文件数量要求
    return uploadedCount >= item.need_file_num
  })
})

const completeButtonText = computed(() => {
  if (uploading.value) {
    return '上传中...'
  }

  if (!isUploadComplete.value) {
    // 显示还需要上传的文件数量
    const incompleteItems = items.value.filter((item) => {
      if (item.need_file_num === 0) return false
      const uploadedCount = files.value.filter((file) => file.item === item.id).length
      return uploadedCount < item.need_file_num
    })

    if (incompleteItems.length > 0) {
      const firstIncomplete = incompleteItems[0]
      const uploadedCount = files.value.filter((file) => file.item === firstIncomplete.id).length
      const needed = firstIncomplete.need_file_num - uploadedCount
      return `还需上传${needed}个文件`
    }
  }

  return '完成监督'
})

const getStateClass = (state) => {
  if (state === '不符合') return 'text-red-600'
  if (state === '符合') return 'text-green-600'
  return 'text-gray-500'
}

const fetchRecordDetail = async () => {
  try {
    loading.value = true
    const response = await getRecordDetail(recordId)
    if (response.code === 200) {
      record.value = response.data.record
      items.value = response.data.items
      files.value = response.data.files
      context.value = response.data.order?.context || ''
      order.value = response.data.order
      activeTab.value = items.value[0]?.name || ''
    }
  } catch (error) {
    ElMessage.error('获取记录详情失败')
  } finally {
    loading.value = false
  }
}

const getFiles = (itemId) => {
  return files.value.filter((file) => file.item == itemId)
}

const openMediaTypeDialog = (itemId) => {
  if (uploading.value) return
  const item = items.value.find((i) => i.id === itemId)
  currentItemId.value = itemId
  currentItemName.value = item?.name
  selectedCategory.value = null
  mediaTypeDialogVisible.value = true
}

const categorySelected = () => {}

const selectMediaType = () => {
  mediaTypeDialogVisible.value = false
  triggerUpload(currentItemId.value)
}

const cancelMediaTypeDialog = () => {
  mediaTypeDialogVisible.value = false
  selectedCategory.value = null
}

const triggerUpload = (itemId) => {
  if (fileInput.value[itemId]) {
    fileInput.value[itemId].remove()
  }

  fileInput.value[itemId] = document.createElement('input')
  fileInput.value[itemId].type = 'file'
  fileInput.value[itemId].accept = currentItemName.value === '满意度视频' ? 'video/*' : 'image/*'
  fileInput.value[itemId].capture = 'camera'
  fileInput.value[itemId].classList.add('hidden')
  fileInput.value[itemId].onchange = (event) =>
    handleFileUpload(event, itemId, currentItemName.value)
  document.body.appendChild(fileInput.value[itemId])

  fileInput.value[itemId].click()
}

const handleFileUpload = async (event, itemId, itemName) => {
  const file = event.target.files[0]
  if (!file) return

  // 重置上传状态
  uploading.value = true
  progress.value = 0
  uploadSpeed.value = 0
  uploadedSize.value = 0
  totalSize.value = file.size
  lastTime.value = 0
  lastLoaded.value = 0

  let uploadFile = file

  // 图片压缩处理
  if (itemName !== '满意度视频') {
    ElMessage.info('图片压缩中，请稍候...')
    progress.value = 10 // 显示压缩开始
    try {
      const options = {
        maxWidthOrHeight: 1600,
        maxSizeMB: 0.8,
        initialQuality: 0.9,
        useWebWorker: true,
        libURL: '/src/network/browser-image-compression.js',
      }
      uploadFile = await imageCompression(file, options)
      progress.value = 20 // 压缩完成

      if (uploadFile.size < 200 * 1024) {
        const retryOptions = { ...options, initialQuality: 0.95 }
        uploadFile = await imageCompression(file, retryOptions)
        progress.value = 25 // 重新压缩完成
      }

      // 更新总大小为压缩后的大小
      totalSize.value = uploadFile.size
    } catch (error) {
      console.error('Error compressing image:', error)
      ElMessage.warning('图片压缩失败，将上传原始文件')
      uploadFile = file
      progress.value = 20 // 即使失败也显示一些进度
    }
  } else {
    ElMessage.info('视频准备上传，请稍候...')
    progress.value = 10 // 视频准备阶段
  }

  // 重置时间和大小记录，从实际上传开始计算
  lastTime.value = Date.now()
  lastLoaded.value = 0

  // 准备上传数据
  const formData = new FormData()
  formData.append('order_id', order.value.id)
  formData.append('item_id', itemId)
  formData.append('file', uploadFile)
  formData.append('type', itemName === '满意度视频' ? 'video' : 'photo')
  formData.append('collect_index', selectedCategory.value)

  try {
    const response = await uploadOrderPhoto(formData, {
      onUploadProgress: (progressEvent) => {
        const { loaded, total } = progressEvent

        // 更新已上传大小
        uploadedSize.value = loaded

        // 计算进度百分比 (30% 用于压缩，70% 用于上传)
        const uploadProgress = Math.round((loaded / total) * 70)
        progress.value = 30 + uploadProgress

        // 计算当前瞬时上传速度 (每200ms更新一次速度显示)
        const currentTime = Date.now()
        if (lastTime.value > 0) {
          const timeDiff = (currentTime - lastTime.value) / 1000 // 秒
          const sizeDiff = loaded - lastLoaded.value // 字节

          if (timeDiff >= 0.2) {
            // 至少200ms更新一次速度
            uploadSpeed.value = sizeDiff / timeDiff // 当前瞬时速度 字节/秒

            // 更新记录
            lastTime.value = currentTime
            lastLoaded.value = loaded
          }
        } else {
          // 首次记录
          lastTime.value = currentTime
          lastLoaded.value = loaded
        }
      },
    })

    if (response.code === 200) {
      files.value.push(response.data.file)
      progress.value = 100
      setTimeout(() => {
        uploading.value = false
        progress.value = 0
        uploadSpeed.value = 0
        uploadedSize.value = 0
        totalSize.value = 0
        lastTime.value = 0
        lastLoaded.value = 0
        ElMessage.success('上传成功')
      }, 500) // 短暂延迟显示100%
    }
  } catch (error) {
    ElMessage.error('上传失败')
    uploading.value = false
    progress.value = 0
    uploadSpeed.value = 0
    uploadedSize.value = 0
    totalSize.value = 0
    lastTime.value = 0
    lastLoaded.value = 0
  }

  event.target.value = ''
}

const deleteFile = async (fileId) => {
  ElMessageBox.confirm('确定删除此文件吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const response = await deleteOrderFile({ file_id: fileId })
        if (response.code === 200) {
          files.value = files.value.filter((file) => file.id !== fileId)
          ElMessage.success('删除成功')
        }
      } catch (error) {
        ElMessage.error('删除失败')
      }
    })
    .catch(() => {
      ElMessage.info('已取消删除')
    })
}

const saveContext = async () => {
  try {
    const response = await saveOrderContext({
      person_id: recordId,
      context: context.value,
    })
    if (response.code === 200) {
      order.value = response.data
      ElMessage.success('备注保存成功')
    }
  } catch (error) {
    ElMessage.error('保存备注失败')
  }
}

const openPreview = (file) => {
  previewFile.value = file
  previewDialogVisible.value = true
}

const handleCompleteSupervision = async () => {
  try {
    const response = await completeSupervision({
      person_id: recordId,
    })
    if (response.code === 200) {
      ElMessage.success('监督已完成')
      // 可以选择跳转回列表页面
      // router.push('/records')
    }
  } catch (error) {
    ElMessage.error('完成监督失败')
  }
}

onMounted(() => {
  fetchRecordDetail()
})
</script>
<style scoped>
:deep(.el-tabs__item) {
  padding: 0px 0.4rem !important;
}
</style>
