<template>
  <div class="collection-detail min-h-screen bg-gray-50">
    <header class="header sticky top-0 flex items-center px-6 py-4 bg-white shadow-sm z-20">
      <el-button
        type="text"
        :icon="ChevronLeft"
        class="text-gray-600 p-0 min-w-0 hover:text-blue-600 transition-colors"
        @click="router.push('/collection')"
      />
      <h1 class="text-xl font-semibold text-gray-800 ml-3">信息采集</h1>
    </header>

    <!-- 骨架屏加载 -->
    <div v-if="loading" class="p-6 space-y-6">
      <el-skeleton :rows="10" animated class="w-full" />
      <el-skeleton :rows="2" animated class="w-full mt-4" />
    </div>

    <!-- 主内容 -->
    <div v-else class="container mx-auto px-6 pb-16 pt-4">
      <div class="tabs-container sticky top-16 bg-white shadow-sm rounded-lg z-10">
        <el-tabs v-model="activeTab" class="tabs px-4 py-2">
          <el-tab-pane label="特困人员信息" name="person">
            <el-form
              :model="person"
              :rules="personRules"
              ref="personForm"
              @submit.prevent="handleSubmit"
              class="form space-y-6"
            >
              <el-card shadow="always" class="form-card border-0 rounded-lg shadow-md">
                <h2 class="section-title text-lg font-semibold text-gray-700 mb-6">
                  特困人员基本信息
                </h2>
                <el-form-item label="姓名" prop="name" class="mb-4">
                  <el-input
                    v-model="person.name"
                    required
                    class="w-full rounded-md border-gray-300"
                    :disabled="uploading"
                  />
                </el-form-item>
                <el-form-item label="身份证号" prop="idcard" class="mb-4">
                  <el-input
                    v-model="person.idcard"
                    required
                    @input="calculatePersonDerivedFields"
                    class="w-full rounded-md border-gray-300"
                    :disabled="uploading"
                  />
                </el-form-item>
                <el-form-item label="联系电话" prop="self_phone" class="mb-4">
                  <el-input
                    v-model="person.self_phone"
                    type="tel"
                    required
                    class="w-full rounded-md border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                    :disabled="uploading"
                  />
                </el-form-item>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="性别" class="mb-4">
                      <el-input
                        :value="personGender"
                        readonly
                        class="w-full rounded-md border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="年龄" class="mb-4">
                      <el-input
                        :value="personAge"
                        type="number"
                        readonly
                        class="w-full rounded-md border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-form-item label="人员照片" prop="personPhoto" class="mb-4">
                  <div class="photo-upload flex items-center gap-4">
                    <div
                      v-if="personPhoto"
                      class="w-24 h-24 rounded-lg overflow-hidden border border-gray-200"
                    >
                      <img
                        :src="personPhoto"
                        alt="特困人员照片"
                        class="w-full h-full object-cover"
                      />
                    </div>
                    <div
                      v-else
                      class="w-24 h-24 flex items-center justify-center bg-gray-100 text-gray-500 text-sm rounded-lg border border-gray-200"
                    >
                      无照片
                    </div>
                    <div class="upload-buttons flex gap-3">
                      <el-button
                        type="primary"
                        :icon="Camera"
                        @click="$refs.personPhotoInput.click()"
                        class="bg-blue-600 hover:bg-blue-700 text-white rounded-md px-4 py-2"
                        :disabled="uploading"
                      >
                        上传照片
                      </el-button>
                      <input
                        ref="personPhotoInput"
                        type="file"
                        accept="image/*"
                        class="hidden"
                        @change="takePhoto('person', $event)"
                      />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item label="家庭位置" prop="address" class="mb-4">
                  <el-input
                    v-model="person.address"
                    type="textarea"
                    placeholder="请输入详细地址"
                    rows="3"
                    required
                    class="w-full rounded-md border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                    :disabled="uploading"
                  />
                </el-form-item>
                <el-form-item label="自理能力等级" prop="man_state_str" class="mb-4">
                  <el-select
                    v-model="person.man_state_str"
                    placeholder="请选择"
                    required
                    class="w-full rounded-md border-gray-300"
                    :disabled="uploading"
                  >
                    <el-option label="全自理" value="全自理" />
                    <el-option label="半失能" value="半失能" />
                    <el-option label="失能" value="失能" />
                  </el-select>
                </el-form-item>
                <el-form-item label="居家状态" prop="live_state" class="mb-4">
                  <el-select
                    v-model="person.live_state"
                    placeholder="请选择"
                    required
                    @change="handleLiveStateChange"
                    class="w-full rounded-md border-gray-300"
                    :disabled="uploading"
                  >
                    <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                    <!-- <el-option label="本村独居" value="本村独居" />
                    <el-option label="本村与照料人同住" value="本村与照料人同住" />
                    <el-option label="县城内与照料人同住" value="县城内与照料人同住" />
                    <el-option label="德州市内与照料人同住" value="德州市内与照料人同住" />
                    <el-option label="德州市外与照料人同住" value="德州市外与照料人同住" />
                    <el-option label="入住精神病医院" value="入住精神病医院" /> -->
                  </el-select>
                </el-form-item>
                <el-form-item
                  v-if="person.live_state === '德州市外与照料人同住'"
                  label="详细地址"
                  prop="live_state_remark"
                  class="mb-4"
                >
                  <el-input
                    v-model="person.live_state_remark"
                    type="textarea"
                    placeholder="请输入详细地址"
                    rows="3"
                    required
                    class="w-full rounded-md border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                    :disabled="uploading"
                  />
                </el-form-item>
              </el-card>
              <el-button
                type="primary"
                native-type="submit"
                class="submit-btn w-full bg-blue-600 hover:bg-blue-700 text-white rounded-md py-3 flex items-center justify-center gap-2"
                :disabled="uploading"
              >
                <el-icon><Save /></el-icon> 保存
              </el-button>
            </el-form>
          </el-tab-pane>

          <el-tab-pane label="照料人信息" name="caretaker">
            <el-form
              :model="caretaker"
              :rules="caretakerRules"
              ref="caretakerForm"
              @submit.prevent="handleSubmit"
              class="form space-y-6"
            >
              <el-card shadow="always" class="form-card border-0 rounded-lg shadow-md">
                <h2 class="section-title text-lg font-semibold text-gray-700 mb-6">
                  照料人基本信息
                </h2>
                <el-form-item label="姓名" prop="relation_name" class="mb-4">
                  <el-input
                    v-model="caretaker.relation_name"
                    placeholder="请输入照料人姓名"
                    required
                    class="w-full rounded-md border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                    :disabled="uploading"
                  />
                </el-form-item>
                <el-form-item label="身份证号" prop="relation_idcard" class="mb-4">
                  <el-input
                    v-model="caretaker.relation_idcard"
                    placeholder="请输入照料人身份证号"
                    required
                    @input="calculateCaretakerDerivedFields"
                    class="w-full rounded-md border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                    :disabled="uploading"
                  />
                </el-form-item>
                <el-form-item label="联系电话" prop="relation_phone" class="mb-4">
                  <el-input
                    v-model="caretaker.relation_phone"
                    placeholder="请输入照料人联系电话"
                    type="tel"
                    required
                    class="w-full rounded-md border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                    :disabled="uploading"
                  />
                </el-form-item>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="性别" class="mb-4">
                      <el-input
                        :value="caretakerGender"
                        readonly
                        class="w-full rounded-md border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="年龄" class="mb-4">
                      <el-input
                        :value="caretakerAge"
                        type="number"
                        readonly
                        class="w-full rounded-md border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-form-item label="与特困人员关系" prop="relation" class="mb-4">
                  <el-input
                    v-model="caretaker.relation"
                    placeholder="请输入与特困人员关系"
                    required
                    class="w-full rounded-md border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                    :disabled="uploading"
                  />
                </el-form-item>
                <el-form-item label="照料人居住地" prop="relation_address" class="mb-4">
                  <el-input
                    v-model="caretaker.relation_address"
                    placeholder="请输入照料人居住地"
                    required
                    class="w-full rounded-md border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                    :disabled="uploading"
                  />
                </el-form-item>
                <el-form-item label="照料人照片" prop="caretakerPhoto" class="mb-4">
                  <div class="photo-upload flex items-center gap-4">
                    <div
                      v-if="caretakerPhoto"
                      class="w-24 h-24 rounded-lg overflow-hidden border border-gray-200"
                    >
                      <img
                        :src="caretakerPhoto"
                        alt="照料人照片"
                        class="w-full h-full object-cover"
                      />
                    </div>
                    <div
                      v-else
                      class="w-24 h-24 flex items-center justify-center bg-gray-100 text-gray-500 text-sm rounded-lg border border-gray-200"
                    >
                      无照片
                    </div>
                    <div class="upload-buttons flex gap-3">
                      <el-button
                        type="primary"
                        :icon="Camera"
                        @click="$refs.caretakerPhotoInput.click()"
                        class="bg-blue-600 hover:bg-blue-700 text-white rounded-md px-4 py-2"
                        :disabled="uploading"
                      >
                        上传照片
                      </el-button>
                      <input
                        ref="caretakerPhotoInput"
                        type="file"
                        accept="image/*"
                        class="hidden"
                        @change="takePhoto('caretaker', $event)"
                      />
                    </div>
                  </div>
                </el-form-item>
              </el-card>
              <el-button
                type="primary"
                native-type="submit"
                class="submit-btn w-full bg-blue-600 hover:bg-blue-700 text-white rounded-md py-3 flex items-center justify-center gap-2"
                :disabled="uploading"
              >
                <el-icon><Save /></el-icon> 保存
              </el-button>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getCollectionDetail, saveCollection, getSysConfig } from '@/api/request'
import { ElMessage } from 'element-plus'
import { Camera, ChevronLeft, Save } from 'lucide-vue-next'
import imageCompression from 'browser-image-compression' // 引入压缩库

const router = useRouter()
const route = useRoute()
const personId = route.params.id
const options = ref({
  list: {
    wnr_live_state: {
      option_value: {
        value: [],
      },
    },
  },
})

const person = ref({
  name: '',
  idcard: '',
  self_phone: '',
  address: '',
  man_state_str: '',
  live_state: '',
  live_state_remark: '',
})
const caretaker = ref({
  relation_name: '',
  relation_idcard: '',
  relation_phone: '',
  relation: '',
  relation_address: '',
})
const personPhoto = ref(null)
const caretakerPhoto = ref(null)
const personPhotoChanged = ref(false)
const caretakerPhotoChanged = ref(false)
const activeTab = ref('person')
const loading = ref(true)
const uploading = ref(false) // 新增上传状态

// 计算衍生字段
const personAge = computed(() => {
  const idcard = person.value.idcard
  if (idcard && validateIdCard(idcard)) {
    const birthYear = parseInt(idcard.substr(6, 4))
    const currentYear = new Date().getFullYear()
    return currentYear - birthYear
  }
  return ''
})

const personGender = computed(() => {
  const idcard = person.value.idcard
  if (idcard && validateIdCard(idcard)) {
    const genderDigit = parseInt(idcard[16])
    return genderDigit % 2 === 1 ? '男' : '女'
  }
  return ''
})

const caretakerAge = computed(() => {
  const idcard = caretaker.value.relation_idcard
  if (idcard && validateIdCard(idcard)) {
    const birthYear = parseInt(idcard.substr(6, 4))
    const currentYear = new Date().getFullYear()
    return currentYear - birthYear
  }
  return ''
})

const caretakerGender = computed(() => {
  const idcard = caretaker.value.relation_idcard
  if (idcard && validateIdCard(idcard)) {
    const genderDigit = parseInt(idcard[16])
    return genderDigit % 2 === 1 ? '男' : '女'
  }
  return ''
})

// 身份证验证函数
const validateIdCard = (idcard) => {
  if (!idcard || idcard.length !== 18) return false

  // 验证格式
  const reg = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)$/
  if (!reg.test(idcard)) return false

  // 校验位权重
  const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
  const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']

  // 计算校验和
  let sum = 0
  for (let i = 0; i < 17; i++) {
    sum += parseInt(idcard[i]) * weights[i]
  }
  const mod = sum % 11
  const checkCode = checkCodes[mod]

  // 验证校验位
  return idcard[17].toUpperCase() === checkCode
}

// 表单验证规则
const personRules = ref({
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  idcard: [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    { len: 18, message: '身份证号必须为18位', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!validateIdCard(value)) {
          callback(new Error('请输入有效的身份证号'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
  self_phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  address: [{ required: true, message: '请输入家庭位置', trigger: 'blur' }],
  man_state_str: [{ required: true, message: '请选择自理能力等级', trigger: 'change' }],
  live_state: [{ required: true, message: '请选择居家状态', trigger: 'change' }],
  live_state_remark: [
    {
      required: true,
      message: '请输入详细地址',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (person.value.live_state === '德州市外与照料人同住' && !value) {
          callback(new Error('请输入详细地址'))
        } else {
          callback()
        }
      },
    },
  ],
  personPhoto: [
    {
      required: true,
      message: '请上传人员照片',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (!personPhoto.value) {
          callback(new Error('请上传人员照片'))
        } else {
          callback()
        }
      },
    },
  ],
})

const caretakerRules = ref({
  relation_name: [{ required: true, message: '请输入照料人姓名', trigger: 'blur' }],
  relation_idcard: [
    { required: true, message: '请输入照料人身份证号', trigger: 'blur' },
    { len: 18, message: '身份证号必须为18位', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!validateIdCard(value)) {
          callback(new Error('请输入有效的身份证号'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
  relation_phone: [{ required: true, message: '请输入照料人联系电话', trigger: 'blur' }],
  relation: [{ required: true, message: '请输入与特困人员关系', trigger: 'blur' }],
  relation_address: [{ required: true, message: '请输入照料人居住地', trigger: 'blur' }],
  caretakerPhoto: [
    {
      required: true,
      message: '请上传照料人照片',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (!caretakerPhoto.value) {
          callback(new Error('请上传照料人照片'))
        } else {
          callback()
        }
      },
    },
  ],
})

//获取名为wnr_live_state的系统配置
const getOptions = () => {
  console.log('获取居家状态选项...')
  return getSysConfig('wnr_live_state').then((response) => {
    console.log('居家状态选项:', response)
    if (response.msg === 2) {
      options.value = response.list.wnr_live_state.option_value.value
    } else {
      ElMessage.error('获取居家状态选项失败')
      return null
    }
  })
}

// 获取人员详情
const fetchDetail = async () => {
  try {
    loading.value = true
    const response = await getCollectionDetail(personId)
    if (response.code === 200) {
      person.value = {
        ...response.data.person,
        live_state_remark: response.data.person.live_state_remark || '',
      }
      caretaker.value = {
        relation_name: response.data.person.relation_name || '',
        relation_idcard: response.data.person.relation_idcard || '',
        relation_phone: response.data.person.relation_phone || '',
        relation: response.data.person.relation || '',
        relation_address: response.data.person.relation_address || '',
      }
      personPhoto.value = response.data.person_photo
        ? `/storage/${response.data.person_photo}`
        : null
      caretakerPhoto.value = response.data.caretaker_photo
        ? `/storage/${response.data.caretaker_photo}`
        : null
      personPhotoChanged.value = false
      caretakerPhotoChanged.value = false
    }
  } catch (error) {
    ElMessage.error('获取人员详情失败')
  } finally {
    loading.value = false
  }
}

// 更新衍生字段触发
const calculatePersonDerivedFields = () => {
  // 触发计算属性更新
  person.value.idcard = person.value.idcard
}

const calculateCaretakerDerivedFields = () => {
  // 触发计算属性更新
  caretaker.value.relation_idcard = caretaker.value.relation_idcard
}

// 拍照功能改为文件上传并压缩
const takePhoto = async (type, event) => {
  const file = event.target.files[0]
  if (!file) return

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请上传图片文件')
    return
  }

  // 验证文件大小（例如限制为5MB）
  const maxSize = 5 * 1024 * 1024 // 5MB
  if (file.size > maxSize) {
    ElMessage.error('图片大小不能超过5MB')
    return
  }

  uploading.value = true
  ElMessage.info('图片压缩中，请稍候...')

  let compressedFile = file
  try {
    const options = {
      maxWidthOrHeight: 1600,
      maxSizeMB: 0.8,
      initialQuality: 0.9,
      useWebWorker: true,
    }
    compressedFile = await imageCompression(file, options)

    // 如果压缩后文件小于 200KB，尝试更高品质
    if (compressedFile.size < 200 * 1024) {
      const retryOptions = { ...options, initialQuality: 0.95 }
      compressedFile = await imageCompression(file, retryOptions)
    }
  } catch (error) {
    console.error('Error compressing image:', error)
    ElMessage.warning('图片压缩失败，将使用原始文件')
    compressedFile = file // 压缩失败时使用原文件
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    const photoUrl = e.target.result
    if (type === 'person') {
      personPhoto.value = photoUrl
      personPhotoChanged.value = true
    } else if (type === 'caretaker') {
      caretakerPhoto.value = photoUrl
      caretakerPhotoChanged.value = true
    }
    // 重置输入框以允许再次上传同一文件
    event.target.value = ''
    uploading.value = false
  }
  reader.onerror = () => {
    ElMessage.error('读取图片失败')
    uploading.value = false
  }
  reader.readAsDataURL(compressedFile)
}

// 处理居家状态变化
const handleLiveStateChange = () => {
  if (person.value.live_state !== '德州市外与照料人同住') {
    person.value.live_state_remark = ''
  }
}

// 保存数据
const handleSubmit = async () => {
  try {
    await Promise.all([
      new Promise((resolve, reject) => {
        if (activeTab.value === 'person') {
          personForm.value.validate((valid) => {
            if (valid) resolve()
            else reject(new Error('特困人员信息验证失败'))
          })
        } else {
          resolve()
        }
      }),
      new Promise((resolve, reject) => {
        if (activeTab.value === 'caretaker') {
          caretakerForm.value.validate((valid) => {
            if (valid) resolve()
            else reject(new Error('照料人信息验证失败'))
          })
        } else {
          resolve()
        }
      }),
    ])

    const data = {
      id: personId,
      name: person.value.name,
      idcard: person.value.idcard,
      phone: person.value.self_phone,
      sex: personGender.value,
      age: personAge.value,
      address: person.value.address,
      man_state_str: person.value.man_state_str,
      live_state: person.value.live_state,
      live_state_remark: person.value.live_state_remark,
      relation_name: caretaker.value.relation_name,
      relation_idcard: caretaker.value.relation_idcard,
      relation_phone: caretaker.value.relation_phone,
      relation_sex: caretakerGender.value,
      relation_age: caretakerAge.value,
      relation: caretaker.value.relation,
      relation_address: caretaker.value.relation_address,
    }

    if (personPhotoChanged.value && personPhoto.value) {
      data.person_photo = personPhoto.value
    }
    if (caretakerPhotoChanged.value && caretakerPhoto.value) {
      data.caretaker_photo = caretakerPhoto.value
    }

    const response = await saveCollection(data)
    if (response.code === 200) {
      ElMessage.success('保存成功')
      router.push('/collection')
    }
  } catch (error) {
    ElMessage.error(
      '保存失败: ' +
        (error.response?.data?.errors.join(',') ||
          error.response?.data?.message ||
          '请查看是否有数据未填写/图片未上传'),
    )
  }
}

onMounted(() => {
  getOptions()
  fetchDetail()
})

const personForm = ref(null)
const caretakerForm = ref(null)
</script>
