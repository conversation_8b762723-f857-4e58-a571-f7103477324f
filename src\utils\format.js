function formatTimestamp(timestamp, format = 'YYYY-MM-DD HH:mm:ss') {
    // 如果是毫秒级时间戳，保留；如果是秒级，转换成毫秒
    const ts = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp
    const date = new Date(ts + 8 * 60 * 60 * 1000) // 东八区偏移

    const Y = date.getUTCFullYear()
    const M = String(date.getUTCMonth() + 1).padStart(2, '0')
    const D = String(date.getUTCDate()).padStart(2, '0')
    const h = String(date.getUTCHours()).padStart(2, '0')
    const m = String(date.getUTCMinutes()).padStart(2, '0')
    const s = String(date.getUTCSeconds()).padStart(2, '0')

    return format
        .replace(/YYYY/, Y)
        .replace(/MM/, M)
        .replace(/DD/, D)
        .replace(/HH/, h)
        .replace(/mm/, m)
        .replace(/ss/, s)
}

export { formatTimestamp } // 导出函数