<template>
  <div class="layout-container">
    <!-- 内容区域 -->
    <div :class="route.path !== '/login' ? content : ''">
      <router-view />
    </div>

    <!-- 底部导航，仅在非登录页显示 -->
    <el-menu
      v-if="route.path !== '/login'"
      :default-active="activeRoute"
      mode="horizontal"
      class="bottom-nav"
      @select="handleNavigation"
      :ellipsis="false"
    >
      <el-menu-item index="/dashboard">
        <el-icon>
          <House />
        </el-icon>
        <span>首页</span>
      </el-menu-item>
      <el-menu-item index="/collection">
        <el-icon>
          <FileText />
        </el-icon>
        <span>采集</span>
      </el-menu-item>
      <el-menu-item index="/records">
        <el-icon>
          <List />
        </el-icon>
        <span>监管</span>
      </el-menu-item>
      <el-menu-item index="/profile">
        <el-icon>
          <User />
        </el-icon>
        <span>我的</span>
      </el-menu-item>
    </el-menu>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { House, FileText, List, User } from 'lucide-vue-next'

const router = useRouter()
const route = useRoute()

const activeRoute = computed(() => route.path)

const handleNavigation = (path) => {
  router.push(path)
}
</script>

<style scoped>
.layout-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.content {
  flex: 1;
  padding-bottom: 60px; /* 为底部导航留出空间 */
}

.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  border-top: 1px solid #e8e8e8;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  height: 50px;
}

.el-menu-item {
  flex: 1;
  text-align: center;
  padding: 0;
  color: #606266;
}

.el-menu-item.is-active {
  color: #409eff;
}
</style>
