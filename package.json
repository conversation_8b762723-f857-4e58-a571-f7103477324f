{"name": "w<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "format": "prettier --write src/"}, "dependencies": {"@tailwindcss/vite": "^4.0.17", "axios": "^1.8.4", "browser-image-compression": "^2.0.2", "element-plus": "^2.9.7", "lucide-vue-next": "^0.485.0", "mp4-muxer": "^5.2.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.21", "init": "^0.1.2", "npx": "^10.2.2", "postcss": "^8.5.3", "prettier": "3.5.3", "tailwindcss": "^4.0.17", "vite": "^6.2.1", "vite-plugin-vue-devtools": "^7.7.2"}}