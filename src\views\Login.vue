<template>
  <div
    class="login min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 to-indigo-900 relative overflow-hidden"
  >
    <div class="particle-bg absolute inset-0" ref="particleContainer"></div>
    <div
      class="w-full max-w-md bg-gray-800/90 backdrop-blur-xl rounded-2xl shadow-2xl p-8 z-10 transform transition hover:-translate-y-1"
    >
      <div
        class="text-center bg-gradient-to-r from-cyan-500 to-purple-600 rounded-t-2xl -m-8 mb-6 p-6"
      >
        <h2 class="text-2xl font-bold text-white">武城县民政局</h2>
        <p class="text-sm text-white/80 mt-1">未纳入社会化照料服务的特困人员监管系统</p>
      </div>

      <form @submit.prevent="handleLogin" class="space-y-6">
        <div>
          <label for="username" class="block text-sm font-medium text-gray-300 mb-1">用户名</label>
          <div class="relative">
            <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-cyan-500">
              <User class="w-5 h-5" />
            </span>
            <input
              v-model="form.username"
              id="username"
              type="text"
              required
              placeholder="请输入用户名"
              class="w-full pl-10 pr-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
            />
          </div>
        </div>

        <div>
          <label for="password" class="block text-sm font-medium text-gray-300 mb-1">密码</label>
          <div class="relative">
            <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-cyan-500">
              <Lock class="w-5 h-5" />
            </span>
            <input
              v-model="form.password"
              id="password"
              type="password"
              required
              placeholder="请输入密码"
              class="w-full pl-10 pr-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
            />
          </div>
        </div>

        <div class="flex items-center">
          <input
            v-model="form.remember"
            id="remember"
            type="checkbox"
            class="h-4 w-4 text-cyan-500 focus:ring-cyan-500 border-gray-600 rounded bg-gray-700/50"
          />
          <label for="remember" class="ml-2 block text-sm text-gray-300">记住密码</label>
        </div>

        <button
          type="submit"
          :disabled="isLoading"
          class="w-full py-3 px-4 bg-gradient-to-r from-cyan-500 to-purple-600 text-white rounded-lg font-medium hover:from-cyan-400 hover:to-purple-500 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-2 focus:ring-offset-gray-800 transition flex items-center justify-center"
        >
          <span v-if="!isLoading">登录</span>
          <span v-else class="flex items-center">
            <svg
              class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            登录中...
          </span>
        </button>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { login } from '@/api/request'
import { ElMessage } from 'element-plus'
import { User, Lock } from 'lucide-vue-next'

const router = useRouter()
const isLoading = ref(false)
const form = ref({
  username: '',
  password: '',
  remember: false,
})

const particleContainer = ref(null)

// Load saved credentials if they exist
onMounted(() => {
  const savedCredentials = localStorage.getItem('rememberedCredentials')
  if (savedCredentials) {
    const { username, password } = JSON.parse(savedCredentials)
    form.value.username = username
    form.value.password = password
    form.value.remember = true
  }

  const container = particleContainer.value
  const canvas = document.createElement('canvas')
  container.appendChild(canvas)
  const ctx = canvas.getContext('2d')
  canvas.width = window.innerWidth
  canvas.height = window.innerHeight

  const particles = []
  const particleCount = 50

  class Particle {
    constructor() {
      this.x = Math.random() * canvas.width
      this.y = Math.random() * canvas.height
      this.size = Math.random() * 2 + 1
      this.speedX = Math.random() * 0.5 - 0.25
      this.speedY = Math.random() * 0.5 - 0.25
    }

    update() {
      this.x += this.speedX
      this.y += this.speedY
      if (this.x > canvas.width) this.x = 0
      else if (this.x < 0) this.x = canvas.width
      if (this.y > canvas.height) this.y = 0
      else if (this.y < 0) this.y = canvas.height
    }

    draw() {
      ctx.fillStyle = 'rgba(0, 174, 255, 0.5)'
      ctx.beginPath()
      ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2)
      ctx.fill()
    }
  }

  for (let i = 0; i < particleCount; i++) {
    particles.push(new Particle())
  }

  function animate() {
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    particles.forEach((particle) => {
      particle.update()
      particle.draw()
    })
    requestAnimationFrame(animate)
  }

  animate()

  window.addEventListener('resize', () => {
    canvas.width = window.innerWidth
    canvas.height = window.innerHeight
  })
})

const handleLogin = async () => {
  if (!form.value.username || !form.value.password) {
    ElMessage.error('请输入用户名和密码')
    return
  }

  isLoading.value = true

  try {
    const response = await login(form.value.username, form.value.password, 'qian')
    if (response.code === 200) {
      localStorage.setItem('token', response.data.token)

      // Handle remember password
      if (form.value.remember) {
        localStorage.setItem(
          'rememberedCredentials',
          JSON.stringify({
            username: form.value.username,
            password: form.value.password,
          }),
        )
      } else {
        localStorage.removeItem('rememberedCredentials')
      }

      ElMessage.success('登录成功')
      router.push('/dashboard')
    }
  } catch (error) {
    ElMessage.error(error.response?.data?.message || '登录失败')
  } finally {
    isLoading.value = false
  }
}
</script>
