<template>
  <div class="dashboard min-h-screen bg-gray-50">
    <header class="header sticky top-0 px-6 py-4 bg-white shadow-sm z-10">
      <h1 class="text-xl font-semibold text-gray-800">照料人监管系统</h1>
      <p class="text-sm text-gray-500 mt-1">未纳入社会化照料服务的特困人员监管模块</p>
    </header>

    <!-- 骨架屏加载 -->
    <div v-if="loading" class="p-6 space-y-6">
      <el-skeleton :rows="4" animated class="w-full" />
      <el-skeleton :rows="2" animated class="w-full" />
    </div>

    <!-- 主内容 -->
    <div v-else class="p-6">
      <el-row :gutter="20" class="stats">
        <el-col v-for="(stat, index) in stats" :key="index" :span="12">
          <el-card
            shadow="hover"
            class="stat-card border-0 rounded-lg shadow-md hover:shadow-lg transition-shadow bg-white mb-4"
          >
            <div class="stat-content flex flex-col items-center p-4">
              <div class="icon mb-3">
                <Users v-if="stat.title === '特困人员总数'" class="h-6 w-6 text-blue-500" />
                <FileCheck v-if="stat.title === '已采集人数'" class="h-6 w-6 text-green-500" />
                <ClipboardList v-if="stat.title === '本月已监管'" class="h-6 w-6 text-amber-500" />
                <BarChart3 v-if="stat.title === '待监管人数'" class="h-6 w-6 text-indigo-500" />
              </div>
              <p class="value text-3xl font-bold text-gray-800">{{ stat.value }}</p>
              <p class="title text-base font-semibold text-gray-700 mt-2">{{ stat.title }}</p>
              <p class="description text-sm text-gray-500 mt-1">{{ stat.description }}</p>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <div class="quick-actions">
        <h2 class="section-title text-lg font-semibold text-gray-700 mb-4">快捷操作</h2>
        <el-card
          v-for="(action, index) in quickActions"
          :key="index"
          shadow="hover"
          class="action-card border-0 rounded-lg shadow-md hover:shadow-lg transition-shadow bg-white mb-4 cursor-pointer"
          @click="action.action"
        >
          <div class="action-content p-4">
            <h3 class="action-title text-base font-semibold text-gray-800">{{ action.title }}</h3>
            <p class="action-description text-sm text-gray-500 mt-1">{{ action.description }}</p>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { BarChart3, ClipboardList, FileCheck, Users } from 'lucide-vue-next'
import { ElMessage } from 'element-plus'
import { getDashboardStats } from '@/api/request' // 新增 API 调用

const router = useRouter()
const loading = ref(true) // 骨架屏加载状态
const stats = ref([])
const quickActions = ref([
  {
    title: '信息采集',
    description: '采集特困人员基础信息',
    action: () => router.push('/collection'),
  },
  {
    title: '监管记录',
    description: '查看和添加监管记录',
    action: () => router.push('/records'),
  },
])

const fetchDashboardStats = async () => {
  try {
    loading.value = true
    const response = await getDashboardStats()
    if (response.code === 200) {
      stats.value = [
        {
          title: '特困人员总数',
          value: response.data.total_people,
          description: '辖区特困人员',
        },
        {
          title: '已采集人数',
          value: response.data.collected_people,
          description: '已完成基础信息采集',
        },
        {
          title: '本月已监管',
          value: response.data.monitored_this_month,
          description: '已完成监管记录',
        },
        {
          title: '待监管人数',
          value: response.data.pending_monitoring,
          description: '本月未监管人数',
        },
      ]
    } else {
      ElMessage.error('获取统计数据失败: ' + response.message)
    }
  } catch (error) {
    ElMessage.error('获取统计数据失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchDashboardStats()
})
</script>
