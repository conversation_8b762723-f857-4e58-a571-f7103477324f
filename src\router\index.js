import { createRouter, createWebHashHistory } from 'vue-router'

const routes = [
  {
    path: '/collection',
    name: 'CollectionList',
    component: () => import('../views/CollectionList.vue'),
  },
  {
    path: '/collection/:id',
    name: 'CollectionDetail',
    component: () => import('../views/CollectionDetail.vue'),
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('../views/Dashboard.vue'),
  },
  {
    path: '/records',
    name: 'Records',
    component: () => import('../views/Records.vue'),
  },
  {
    path: '/records/:id',
    name: 'RecordDetail',
    component: () => import('../views/RecordDetail.vue'),
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('../views/Profile.vue'),
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
  },
  {
    path: '/',
    redirect: '/login', // 默认跳转到登录页
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes,
})

export default router