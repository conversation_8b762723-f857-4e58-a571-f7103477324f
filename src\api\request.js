import axios from 'axios';
import router from '@/router';
import { ElMessage } from 'element-plus';

// 创建 axios 实例
const instance = axios.create({
  baseURL: '/api',
  timeout: 600000,
});

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response && error.response.status === 401) {
      localStorage.removeItem('token');
      router.push('/');
    }
    return Promise.reject(error);
  }
);

// 防抖缓存
const debounceCache = new Map();

// 防抖函数
const debounce = (func, wait) => {
  let timeout;
  return function (...args) {
    clearTimeout(timeout);
    return new Promise((resolve, reject) => {
      timeout = setTimeout(async () => {
        try {
          const result = await func.apply(this, args);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      }, wait);
    });
  };
};

// 封装通用的请求方法
export const request = async (time, method, url, data = {}, config = {}) => {
  const requestKey = `${method}:${url}`; // 仅使用 method 和 url 作为缓存键

  if (debounceCache.has(requestKey)) {
    return debounceCache.get(requestKey)(data);
  }

  const debouncedRequest = debounce(async (data) => {
    try {
      const response = await instance({
        method,
        url,
        data: data,
        params: method === 'GET' ? data : null,
        ...config,
      });
      return response;
    } catch (error) {
      console.error('API request failed:', error);
      if (error.response && error.response.status === 403) {
        ElMessage.error('无权限访问');
        router.push('/');
      } else if (error.response && error.response.status === 404) {
        ElMessage.error('未找到资源，请稍后再试');
      } else if (error.response && error.response.status === 401) {
        router.push('/');
      } else if (error.response && error.response.status === 400) {
        ElMessage.error(error.response?.data?.infor || '请求参数错误');
      }
      throw error;
    } finally {
      debounceCache.delete(requestKey);
    }
  }, time);

  debounceCache.set(requestKey, debouncedRequest);
  return debouncedRequest(data);
};

// 登录
export const login = (username, password, role) => {
  return request(1000, 'POST', '/login', { username, password, role });
};

export const getBaiduAPI = (lng, lat) => {
  return request(100, 'POST', `/getinfo/getPoi?xy=${lng},${lat}`);
};
export const getPosition = async () => {
  return new Promise((resolve, reject) => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            lng: position.coords.longitude,
            lat: position.coords.latitude,
          });
        },
        (error) => {
          if (error.message == "User denied geolocation prompt") {
            ElMessage.error("定位失败，您拒绝了定位请求");
          } else {
            ElMessage.error("定位失败，请确认您的网络是否畅通，然后刷新试试");
          }
          reject(new Error(error.message));
        }
      );
    } else {
      reject(new Error("Geolocation is not supported by this browser."));
    }
  });
};
export const getOpenid = async (code, role) => {
  return request(100, 'POST', '/getinfo/get-openid', { code, role });
};
export const getJsSdkConfig = async (url) => {
  return request(100, 'POST', '/getinfo/js-sdk', { url });
};
// 获取采集人员列表
export const getCollectionList = () => {
  return request(100, 'GET', '/collection/list');
};

// 获取采集人员详情
export const getCollectionDetail = (id) => {
  return request(100, 'GET', `/collection/detail/${id}`);
};

// 保存采集信息
export const saveCollection = (data) => {
  return request(100, 'POST', '/collection/save', data);
};
// 新增监管记录 API
export const getRecordList = (params = {}) => {
  return request(100, 'GET', '/records/list', params);
};
export const updateVillage = (data) => {
  return request(100, 'POST', '/records/update-village', data);
};

export const getRecordDetail = (id) => {
  return request(100, 'GET', `/records/detail/${id}`);
};
export const saveOrderContext = (data) => {
  return request(500, 'POST', '/records/save-context', data);
};

export const uploadOrderPhoto = (data, config = {}) => {
  return request(100, 'POST', '/records/upload-photo', data, config);
};

export const deleteOrderFile = (data) => {
  return request(100, 'POST', '/records/delete-file', data);
};

export const completeSupervision = (data) => {
  return request(100, 'POST', '/records/complete-supervision', data);
};
export const getDashboardStats = () => {
  return request(100, 'GET', '/dashboard/stats');
};
export const getUserProfile = () => {
  return request(100, 'GET', '/profile');
};
export const getSysConfig = (data) => {
  return request(100, 'GET', '/sysconfig', { option_key: data });
};

export const logout = () => {
  return request(100, 'POST', '/logout');
};