<template>
  <div class="collection-list min-h-screen bg-gray-50">
    <header class="header sticky top-0 flex items-center px-6 py-4 bg-white shadow-sm z-10">
      <el-button
        type="text"
        :icon="ChevronLeft"
        class="text-gray-600 p-0 min-w-0 hover:text-blue-600 transition-colors"
        @click="router.push('/dashboard')"
      />
      <h1 class="text-xl font-semibold text-gray-800 ml-3">信息采集</h1>
    </header>

    <!-- 骨架屏加载 -->
    <div v-if="loading" class="p-6 space-y-4">
      <el-skeleton :rows="1" animated class="w-full" />
      <el-skeleton :rows="1" animated class="w-full" />
      <el-skeleton :rows="5" animated class="w-full" />
    </div>

    <!-- 主内容 -->
    <div v-else>
      <div class="search-bar sticky top-16 px-6 py-4 bg-white shadow-sm z-10">
        <div class="flex flex-col gap-4">
          <el-select
            v-model="selectedTown"
            placeholder="选择乡镇"
            class="w-full rounded-md border-gray-300"
            disabled
          >
            <el-option v-for="town in towns" :key="town.id" :label="town.name" :value="town.id" />
          </el-select>
          <el-select
            v-model="selectedVillage"
            placeholder="选择村"
            class="w-full rounded-md border-gray-300 focus:ring-blue-500 focus:border-blue-500"
            @change="handleVillageChange"
          >
            <el-option
              v-for="big_village in villages"
              :key="big_village"
              :label="big_village"
              :value="big_village"
            />
          </el-select>
          <el-input
            v-model="searchQuery"
            placeholder="搜索姓名或身份证号"
            clearable
            prefix-icon="Search"
            class="w-full rounded-md border-gray-300 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      <main class="main p-6">
        <div class="list space-y-4">
          <el-card
            v-for="person in filteredPeople"
            :key="person.id"
            shadow="hover"
            class="person-card border-0 rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer bg-white"
            @click="handlePersonClick(person.id)"
          >
            <div class="flex justify-between items-center p-4">
              <div class="flex-1">
                <h3 class="text-lg font-semibold text-gray-800">{{ person.name }}</h3>
                <p class="text-sm text-gray-500 mt-1">身份证: {{ person.idcard }}</p>
                <p
                  v-if="person.collect_time"
                  class="text-sm text-gray-500 mt-1 flex items-center gap-1"
                >
                  <el-icon><Clock /></el-icon> {{ formatDate(person.collect_time) }}
                </p>
              </div>
              <div class="flex items-center gap-3">
                <el-tag
                  :type="person.collect_time ? 'success' : 'info'"
                  size="small"
                  class="rounded-full px-2 py-1"
                >
                  {{ person.collect_time ? '已采集' : '未采集' }}
                </el-tag>
                <el-icon class="text-gray-500"><ChevronRight /></el-icon>
              </div>
            </div>
          </el-card>

          <div v-if="filteredPeople.length === 0" class="empty py-8 text-center">
            <el-empty description="未找到匹配的特困人员" :image-size="100" />
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getCollectionList, updateVillage } from '@/api/request'
import { ElMessage } from 'element-plus'
import { ChevronLeft, ChevronRight, Search, Clock } from 'lucide-vue-next'

const router = useRouter()
const searchQuery = ref('')
const people = ref([])
const selectedTown = ref(null)
const selectedVillage = ref(null)
const towns = ref([])
const villages = ref([])
const loading = ref(true) // 骨架屏加载状态

const filteredPeople = computed(() => {
  if (!people.value) return []
  return people.value.filter(
    (person) =>
      (person.name && person.name.includes(searchQuery.value)) ||
      (person.idcard && person.idcard.includes(searchQuery.value)),
  )
})

const formatDate = (timestamp) => {
  if (!timestamp) return '未知时间'
  return new Date(timestamp * 1000).toLocaleDateString('zh-CN')
}

const fetchCollectionList = async () => {
  try {
    loading.value = true
    const response = await getCollectionList({ big_village: selectedVillage.value })
    if (response.code === 200) {
      people.value = response.data.people || []
      towns.value = response.data.towns || []
      villages.value = response.data.villages || []
      selectedTown.value = towns.value[0]?.id || null
      if (!selectedVillage.value && response.data.selected_village) {
        selectedVillage.value = response.data.selected_village
      }
    } else {
      ElMessage.error('获取人员列表失败: ' + response.message)
    }
  } catch (error) {
    ElMessage.error('获取人员列表失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleVillageChange = async (village) => {
  try {
    loading.value = true
    const response = await updateVillage({ big_village: village })
    if (response.code === 200) {
      selectedVillage.value = village
      await fetchCollectionList()
      ElMessage.success('行政村已更新')
    } else {
      ElMessage.error('更新行政村失败: ' + response.message)
    }
  } catch (error) {
    ElMessage.error('更新行政村失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handlePersonClick = (id) => {
  router.push(`/collection/${id}`)
}

onMounted(() => {
  fetchCollectionList()
})
</script>
